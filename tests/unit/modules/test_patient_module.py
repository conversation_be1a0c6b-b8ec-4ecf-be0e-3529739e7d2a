"""
Test PatientModule (M - Mandatory) functionality.

PatientModule implements DICOM PS3.3 C.7.1.1 Patient Module.
Required for all RTDoseIOD instances.
"""
import pydicom
from datetime import datetime, date
from pydicom.valuerep import PersonName
from pyrt_dicom.modules import PatientModule
from pyrt_dicom.enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID
from pyrt_dicom.validators import ValidationResult


class TestPatientModule:
    """Test PatientModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        patient = PatientModule.from_required_elements(
            patient_name="<PERSON><PERSON>^John^Jr^^",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientName == "Doe^John^Jr^^"
        assert dataset.PatientID == "12345"
        assert dataset.PatientBirthDate == "19900101"
        assert dataset.PatientSex == "M"
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test that all patient elements are Type 2 (can be empty but required)
        # All elements have default empty string values per DICOM specification
        patient = PatientModule.from_required_elements()
        
        dataset = patient.to_dataset()
        # Should be able to create with all default empty values
        assert dataset.PatientName == ""
        assert dataset.PatientID == ""
        assert dataset.PatientBirthDate == ""
        assert dataset.PatientSex == ""
        
        # Test with specific empty values (Type 2 - allowed to be empty but not None)
        patient = PatientModule.from_required_elements(
            patient_name="",  # Empty but not None (Type 2)
            patient_id="12345",  # Type 2
            patient_birth_date="",  # Type 2
            patient_sex=""  # Type 2
        )
        
        dataset = patient.to_dataset()
        # Should be able to create with empty Type 2 elements
        assert dataset.PatientName == ""
        assert dataset.PatientID == "12345"
    
    def test_invalid_sex_enum(self):
        """Test invalid patient sex enum handling."""
        # Should be able to create with invalid sex value
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex="INVALID"  # Invalid enum value - should be allowed at creation
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientSex == "INVALID"
        
        # Validation should fail with invalid sex value
        validation_result = patient.validate()
        assert len(validation_result.warnings) > 0  # Invalid enum values generate warnings
        
        # Test with valid sex value
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        validation_result = patient.validate()
        assert len(validation_result.errors) == 0
    
    def test_person_name_formatting(self):
        """Test proper person name formatting."""
        patient = PatientModule.from_required_elements(
            patient_name="Last^First^Middle^Prefix^Suffix",
            patient_id="12345",
            patient_birth_date="",
            patient_sex=""
        )
        
        dataset = patient.to_dataset()
        assert "Last" in str(dataset.PatientName)
        assert "First" in str(dataset.PatientName)
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        ).with_optional_elements(
            patient_comments="Test patient comments",
            quality_control_subject="NO"
        )
        
        dataset = patient.to_dataset()
        assert hasattr(dataset, 'PatientComments')
        assert hasattr(dataset, 'QualityControlSubject')
        assert dataset.PatientComments == "Test patient comments"
        assert dataset.QualityControlSubject == "NO"
    
    def test_patient_id_validation(self):
        """Test patient ID validation."""
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="HOSPITAL123",
            patient_birth_date="",
            patient_sex=""
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientID == "HOSPITAL123"
    
    def test_birth_date_formats(self):
        """Test various birth date formats."""
        valid_dates = ["19900101", "20000229", "19700430"]
        
        for date_str in valid_dates:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="TEST001",
                patient_birth_date=date_str,
                patient_sex=""
            )
            dataset = patient.to_dataset()
            assert dataset.PatientBirthDate == date_str
    
    def test_sex_enum_values(self):
        """Test all valid patient sex enum values."""
        sex_values = [PatientSex.MALE, PatientSex.FEMALE, PatientSex.OTHER]
        
        for sex in sex_values:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="TEST001",
                patient_birth_date="",
                patient_sex=sex
            )
            dataset = patient.to_dataset()
            assert dataset.PatientSex == sex.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        # Test with valid data
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="12345",
            patient_birth_date="",
            patient_sex=""
        )
        
        assert hasattr(patient, 'validate')
        assert callable(patient.validate)
        
        # Test validation result structure
        validation_result = patient.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

        # Test with invalid data
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="12345",  # Valid patient ID
            patient_birth_date="",
            patient_sex="INVALID"  # Invalid enum value
        )
        
        validation_result = patient.validate()
        # Invalid enum values should generate warnings, not errors
        assert len(validation_result.warnings) > 0
        
        # Test validation returns expected structure
        result = patient.validate()
        assert result is not None
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_to_dataset_method(self):
        """Test to_dataset() method returns proper pydicom Dataset."""
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        dataset = patient.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        assert hasattr(dataset, 'PatientName')
        assert hasattr(dataset, 'PatientID')
        assert hasattr(dataset, 'PatientBirthDate')
        assert hasattr(dataset, 'PatientSex')
    
    def test_datetime_formatting(self):
        """Test datetime object formatting for dates and times."""
        birth_date = date(1990, 1, 15)
        birth_time = datetime(2023, 1, 1, 14, 30, 45)
        
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="TEST001",
            patient_birth_date=birth_date,
            patient_sex=PatientSex.FEMALE
        ).with_optional_elements(
            patient_birth_time=birth_time
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientBirthDate == "19900115"
        assert dataset.PatientBirthTime == "143045"
    
    def test_person_name_object(self):
        """Test PersonName object handling."""
        person_name = PersonName("Doe^John^Jr^^MD")
        
        patient = PatientModule.from_required_elements(
            patient_name=person_name,
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientName == person_name
    
    def test_alternative_calendar(self):
        """Test alternative calendar date handling with Type 1C requirement."""
        # Test successful addition with required calendar
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="TEST001",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        ).with_alternative_calendar(
            patient_alternative_calendar="HEBREW",
            patient_birth_date_in_alternative_calendar="5750/10/01"
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientAlternativeCalendar == "HEBREW"
        assert dataset.PatientBirthDateInAlternativeCalendar == "5750/10/01"
        
        # Test Type 1C validation - should fail without calendar
        try:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="TEST001",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE
            ).with_alternative_calendar(
                patient_alternative_calendar="",  # Empty calendar
                patient_birth_date_in_alternative_calendar="5750/10/01"  # But date provided
            )
            assert False, "Should have raised ValueError for missing required calendar"
        except ValueError as e:
            assert "Type 1C requirement" in str(e)
    
    def test_non_human_organism(self):
        """Test non-human organism handling with Type 1C/2C requirements."""
        # Test successful creation with required species info
        patient = PatientModule.from_required_elements(
            patient_name="Lab^Rat^001",
            patient_id="RAT001",
            patient_birth_date="20230101",
            patient_sex=PatientSex.MALE
        ).with_non_human_organism(
            patient_species_description="Rattus rattus",
            patient_breed_description="Sprague Dawley",
            responsible_person="Dr. Smith"
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientSpeciesDescription == "Rattus rattus"
        assert dataset.PatientBreedDescription == "Sprague Dawley"
        assert dataset.ResponsiblePerson == "Dr. Smith"
        assert dataset.PatientBreedCodeSequence == []
        assert dataset.BreedRegistrationSequence == []
        assert dataset.ResponsibleOrganization == ""
        
        # Test properties
        assert patient.is_non_human
        assert not patient.is_human
        
        # Test Type 1C validation - should fail without species info
        try:
            patient = PatientModule.from_required_elements(
                patient_name="Test^Animal",
                patient_id="ANIMAL001",
                patient_birth_date="20230101",
                patient_sex=PatientSex.FEMALE
            ).with_non_human_organism(
                # Missing both species description AND species code sequence
                responsible_person="Dr. Jones"
            )
            assert False, "Should have raised ValueError for missing species info"
        except ValueError as e:
            assert "Type 1C requirement" in str(e)
    
    def test_responsible_person_with_role(self):
        """Test responsible person with required role (Type 1C)."""
        # Test successful creation with role
        patient = PatientModule.from_required_elements(
            patient_name="Pet^Dog",
            patient_id="DOG001",
            patient_birth_date="20220101",
            patient_sex=PatientSex.FEMALE
        ).with_responsible_person(
            responsible_person="Smith^John",
            responsible_person_role=ResponsiblePersonRole.OWNER
        )
        
        dataset = patient.to_dataset()
        assert dataset.ResponsiblePerson == "Smith^John"
        assert dataset.ResponsiblePersonRole == "OWNER"
        
        # Test property
        assert patient.has_responsible_person_with_role
        
        # Test Type 1C validation - should fail with person but no role
        try:
            patient = PatientModule.from_required_elements(
                patient_name="Pet^Cat",
                patient_id="CAT001",
                patient_birth_date="20220101",
                patient_sex=PatientSex.MALE
            ).with_responsible_person(
                responsible_person="Jones^Jane",
                responsible_person_role=""  # Empty role when person is provided
            )
            assert False, "Should have raised ValueError for missing required role"
        except ValueError as e:
            assert "Type 1C requirement" in str(e)
    
    def test_deidentification(self):
        """Test deidentification handling with Type 1C requirements."""
        # Test successful creation with method
        patient = PatientModule.from_required_elements(
            patient_name="",  # Deidentified - empty name
            patient_id="ANON001",
            patient_birth_date="",  # Deidentified - empty date
            patient_sex=PatientSex.OTHER
        ).with_deidentification(
            patient_identity_removed="YES",
            de_identification_method="HIPAA Safe Harbor"
        )
        
        dataset = patient.to_dataset()
        assert dataset.PatientIdentityRemoved == "YES"
        assert dataset.DeIdentificationMethod == "HIPAA Safe Harbor"
        
        # Test properties
        assert patient.is_deidentified
        assert patient.requires_deidentification_method
        
        # Test Type 1C validation - should fail with "YES" but no method
        try:
            patient = PatientModule.from_required_elements(
                patient_name="",
                patient_id="ANON002",
                patient_birth_date="",
                patient_sex=PatientSex.OTHER
            ).with_deidentification(
                patient_identity_removed="YES"
                # Missing both method and method code sequence
            )
            assert False, "Should have raised ValueError for missing deidentification method"
        except ValueError as e:
            assert "Type 1C requirement" in str(e)
    
    def test_module_properties(self):
        """Test module properties and utility methods."""
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="TEST001",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        # Test inherited BaseModule properties
        assert patient.module_name == "PatientModule"
        assert patient.has_data
        assert patient.get_element_count() > 0
        assert "PatientModule" in str(patient)
        assert "elements=" in str(patient)
        
        # Test human/non-human properties
        assert patient.is_human
        assert not patient.is_non_human
        assert not patient.is_deidentified
        assert not patient.has_alternative_calendar_dates
        assert not patient.requires_alternative_calendar
        assert not patient.requires_deidentification_method
    
    def test_static_helper_methods(self):
        """Test static helper methods for sequence items."""
        # Test other patient ID item creation
        other_id_item = PatientModule.create_other_patient_id_item(
            patient_id="BARCODE123456",
            type_of_patient_id=TypeOfPatientID.BARCODE
        )
        
        assert isinstance(other_id_item, pydicom.Dataset)
        assert other_id_item.PatientID == "BARCODE123456"
        assert other_id_item.TypeOfPatientID == "BARCODE"
        
        # Test strain stock item creation
        strain_registry_code = pydicom.Dataset()
        strain_registry_code.CodeValue = "123456"
        strain_registry_code.CodingSchemeDesignator = "DCM"
        strain_registry_code.CodeMeaning = "Test Registry"
        
        strain_item = PatientModule.create_strain_stock_item(
            strain_stock_number="SS001",
            strain_source="TestLab",
            strain_source_registry_code=strain_registry_code
        )
        
        assert isinstance(strain_item, pydicom.Dataset)
        assert strain_item.StrainStockNumber == "SS001"
        assert strain_item.StrainSource == "TestLab"
        assert hasattr(strain_item, 'StrainSourceRegistryCodeSequence')
        assert len(strain_item.StrainSourceRegistryCodeSequence) == 1
        
        # Test genetic modification item creation
        genetic_mod_item = PatientModule.create_genetic_modification_item(
            genetic_modifications_description="Test modification",
            genetic_modifications_nomenclature="MGI_2013"
        )
        
        assert isinstance(genetic_mod_item, pydicom.Dataset)
        assert genetic_mod_item.GeneticModificationsDescription == "Test modification"
        assert genetic_mod_item.GeneticModificationsNomenclature == "MGI_2013"