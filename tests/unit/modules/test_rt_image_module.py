"""
Test RTImageModule functionality.

RTImageModule implements DICOM PS3.3 C.8.8.2 RT Image Module.
Describes RT-specific characteristics of a projection image.
"""

import pytest
from pyrt_dicom.modules import RTImageModule
from pyrt_dicom.enums.rt_enums import (
    RTImagePlane, PrimaryDosimeterUnit, PixelIntensityRelationshipSign,
    RTImageTypeValue3, RTBeamLimitingDeviceType, BlockType, BlockDivergence,
    BlockMountingPosition, FluenceDataSource, EnhancedRTBeamLimitingDeviceDefinitionFlag
)
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PixelRepresentation
from pyrt_dicom.enums.series_enums import PatientPosition
from pyrt_dicom.validators import ValidationResult


class TestRTImageModule:
    """Test RTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Access attributes through to_dataset() method
        dataset = rt_image.to_dataset()
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.PixelRepresentation == 0  # UNSIGNED
        assert dataset.RTImageLabel == "Portal Image 1"
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "PORTAL"]
        assert dataset.RTImagePlane == "NORMAL"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default empty values for Type 2 elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        
        # Type 2 element should be empty string
        dataset = rt_image.to_dataset()
        assert dataset.ConversionType == ""
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            rt_image_name="Portal verification image",
            rt_image_description="Verification image for beam 1",
            radiation_machine_name="Varian TrueBeam",
            primary_dosimeter_unit=PrimaryDosimeterUnit.MU,
            radiation_machine_sad=1000.0,
            rt_image_sid=1500.0
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.RTImageName == "Portal verification image"
        assert dataset.RTImageDescription == "Verification image for beam 1"
        assert dataset.RadiationMachineName == "Varian TrueBeam"
        assert dataset.PrimaryDosimeterUnit == "MU"
        assert dataset.RadiationMachineSAD == 1000.0
        assert dataset.RTImageSID == 1500.0
    
    def test_with_pixel_intensity_relationship_sign(self):
        """Test adding pixel intensity relationship sign."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_optional_elements(
            pixel_intensity_relationship="LIN"
        ).with_pixel_intensity_relationship_sign(
            pixel_intensity_relationship_sign=PixelIntensityRelationshipSign.POSITIVE
        )
        
        dataset = rt_image.to_dataset()
        assert dataset.PixelIntensityRelationship == "LIN"
        assert dataset.PixelIntensityRelationshipSign == 1  # POSITIVE
    
    def test_with_exposure_sequence(self):
        """Test adding exposure sequence."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0,
            exposure_time=100.0,
            meterset_exposure=50.0
        )
        
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        dataset = rt_image.to_dataset()
        assert hasattr(dataset, 'ExposureSequence')
        assert len(dataset.ExposureSequence) == 1
        assert dataset.ExposureSequence[0].KVP == 120.0
        assert dataset.ExposureSequence[0].XRayTubeCurrent == 200.0
        assert dataset.ExposureSequence[0].ExposureTime == 100.0
        assert dataset.ExposureSequence[0].MetersetExposure == 50.0
    
    def test_create_beam_limiting_device_item(self):
        """Test creating beam limiting device item."""
        device_item = RTImageModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.ASYMX,
            number_of_leaf_jaw_pairs=1,
            source_to_beam_limiting_device_distance=400.0,
            leaf_jaw_positions=[-50.0, 50.0]
        )
        
        assert device_item.RTBeamLimitingDeviceType == "ASYMX"
        assert device_item.NumberOfLeafJawPairs == 1
        assert device_item.SourceToBeamLimitingDeviceDistance == 400.0
        assert device_item.LeafJawPositions == [-50.0, 50.0]
    
    def test_create_block_item(self):
        """Test creating block item."""
        block_item = RTImageModule.create_block_item(
            block_number=1,
            block_type=BlockType.SHIELDING,
            block_divergence=BlockDivergence.PRESENT,
            source_to_block_tray_distance=500.0,
            material_id="LEAD",
            block_number_of_points=4,
            block_data=[-50.0, -50.0, 50.0, -50.0, 50.0, 50.0, -50.0, 50.0],
            block_name="Custom Block",
            block_thickness=70.0
        )
        
        assert block_item.BlockNumber == 1
        assert block_item.BlockType == "SHIELDING"
        assert block_item.BlockDivergence == "PRESENT"
        assert block_item.SourceToBlockTrayDistance == 500.0
        assert block_item.MaterialID == "LEAD"
        assert block_item.BlockNumberOfPoints == 4
        assert len(block_item.BlockData) == 8  # 4 points (x,y)
        assert block_item.BlockName == "Custom Block"
        assert block_item.BlockThickness == 70.0
    
    def test_property_methods(self):
        """Test property methods."""
        # Test portal image
        portal_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert portal_image.is_portal_image is True
        assert portal_image.is_simulator_image is False
        assert portal_image.is_fluence_map is False
        
        # Test fluence map
        fluence_map = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Fluence Map",
            image_type=["ORIGINAL", "PRIMARY", "FLUENCE"]
        )
        assert fluence_map.is_fluence_map is True
        
        # Test exposure data
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        ).with_exposure_sequence([exposure_item])
        
        assert rt_image.has_exposure_data is True
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        
        assert hasattr(rt_image, 'validate')
        assert callable(rt_image.validate)
        
        # Test validation result structure
        validation_result = rt_image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    
    def test_method_chaining(self):
        """Test that methods support chaining."""
        exposure_item = RTImageModule.create_exposure_item(
            kvp=120.0,
            x_ray_tube_current=200.0
        )
        
        rt_image = (RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Chained Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        .with_optional_elements(
            rt_image_name="Chained Name",
            radiation_machine_name="Varian TrueBeam"
        )
        .with_exposure_sequence([exposure_item]))
        
        dataset = rt_image.to_dataset()
        assert dataset.RTImageLabel == "Chained Image"
        assert dataset.RTImageName == "Chained Name"
        assert dataset.RadiationMachineName == "Varian TrueBeam"
        assert rt_image.has_exposure_data is True
    
    def test_to_dataset_method(self):
        """Test that to_dataset() generates valid pydicom Dataset."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Dataset Test Image",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        ).with_optional_elements(
            rt_image_name="Test Name",
            radiation_machine_name="Test Machine"
        )
        
        # Test dataset generation
        dataset = rt_image.to_dataset()
        
        # Verify it's a proper pydicom Dataset
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        
        # Verify all attributes are present
        assert dataset.SamplesPerPixel == 1
        assert dataset.RTImageLabel == "Dataset Test Image"
        assert dataset.RTImageName == "Test Name"
        assert dataset.RadiationMachineName == "Test Machine"
        
        # Verify dataset has expected length
        assert len(dataset) > 0
        
        # Test that multiple calls return independent copies
        dataset2 = rt_image.to_dataset()
        assert dataset is not dataset2  # Different objects
        assert dataset.RTImageLabel == dataset2.RTImageLabel  # Same data
    
    def test_module_base_properties(self):
        """Test inherited BaseModule properties work correctly."""
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Properties Test",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Test BaseModule properties
        assert rt_image.module_name == "RTImageModule"
        assert rt_image.has_data is True
        assert rt_image.get_element_count() > 0
        
        # Test string representation
        repr_str = repr(rt_image)
        assert "RTImageModule" in repr_str
        assert "elements=" in repr_str
