"""Base validation framework for DICOM modules."""

import re
from dataclasses import dataclass
from typing import Any
from pydicom import Dataset
from ..validation_result import ValidationResult


@dataclass
class ValidationConfig:
    """Configuration for DICOM validation behavior."""
    strict_mode: bool = False
    check_enumerated_values: bool = True
    validate_sequences: bool = True
    validate_required_elements: bool = True
    validate_conditional_requirements: bool = True
    validate_semantic_constraints: bool = True
    validate_cross_field_dependencies: bool = True


class BaseValidator:
    """Base validation utilities for DICOM modules."""
    
    @staticmethod
    def validate_enumerated_value(
        value: Any, 
        allowed_values: list[str], 
        field_name: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate enumerated values against allowed list.
        
        Args:
            value: Value to validate
            allowed_values: List of allowed string values
            field_name: DICOM field name for error reporting
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if value and str(value) not in allowed_values:
            message = f"{field_name} value '{value}' should be one of: {', '.join(allowed_values)}"
            result.add_warning(message)
        
        return result
    
    @staticmethod
    def validate_conditional_requirement(
        condition: bool,
        required_fields: list[str],
        dataset: Dataset,
        error_message: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate Type 1C conditional requirements.
        
        Args:
            condition: Whether the condition is met
            required_fields: List of field names that should be present
            dataset: Dataset to check for field presence
            error_message: Error message if requirement not met
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if condition:
            missing_fields = [field for field in required_fields if not hasattr(dataset, field)]
            if len(missing_fields) == len(required_fields):
                result.add_error(error_message)
        return result
    
    @staticmethod
    def validate_either_or_requirement(
        condition: bool,
        field_a: str,
        field_b: str,
        dataset: Dataset,
        error_message: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate either/or conditional requirements.
        
        Args:
            condition: Whether the condition is met
            field_a: First alternative field name
            field_b: Second alternative field name  
            dataset: Dataset to check
            error_message: Error message if neither field present
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if condition:
            has_a = hasattr(dataset, field_a) and getattr(dataset, field_a, None)
            has_b = hasattr(dataset, field_b) and getattr(dataset, field_b, None)
            if not has_a and not has_b:
                result.add_error(error_message)
        return result
        
    @staticmethod
    def validate_uid_format(uid: str, field_name: str, result: ValidationResult) -> None:
        """Validate UID format according to DICOM PS3.5 standard.
        
        Args:
            uid: UID string to validate
            field_name: Name of the field being validated (for error messages)
            result: ValidationResult instance to update with errors
        """
        if not uid:
            result.add_error(f"{field_name} cannot be empty")
            return
            
        # UID format validation according to DICOM PS3.5
        # UIDs are composed of numeric components separated by periods
        # Each component must start with 0-9 and not start with 0 unless it's a single 0
        # Maximum length is 64 characters total
        
        if len(uid) > 64:
            result.add_error(
                f"{field_name} exceeds maximum length of 64 characters. "
                f"Current length: {len(uid)}. UID: {uid}"
            )
            
        if not re.match(r'^[0-9]+(\.[0-9]+)*$', uid):
            result.add_error(
                f"{field_name} contains invalid characters. "
                "Only digits and periods are allowed. "
                f"Invalid UID: {uid}"
            )
            
        # Check for leading zeros in components (except single zero)
        components = uid.split('.')
        for i, comp in enumerate(components):
            if comp.startswith('0') and len(comp) > 1:
                result.add_error(
                    f"{field_name} component {i+1} has leading zeros. "
                    f"Component: '{comp}' in UID: {uid}"
                )
    
    @staticmethod
    def create_validation_result() -> ValidationResult:
        """Create a new ValidationResult instance.
        
        Returns:
            New ValidationResult instance
        """
        return ValidationResult()
    
    @staticmethod
    def create_validation_result_from_lists(errors: list[str], warnings: list[str]) -> ValidationResult:
        """Create ValidationResult instance from error and warning lists.
        
        Args:
            errors: List of validation errors
            warnings: List of validation warnings
            
        Returns:
            ValidationResult instance populated with the provided errors and warnings
        """
        result = ValidationResult()
        result.extend_errors(errors)
        result.extend_warnings(warnings)
        return result