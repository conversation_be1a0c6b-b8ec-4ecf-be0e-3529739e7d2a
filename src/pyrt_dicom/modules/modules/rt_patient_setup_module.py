"""RT Patient Setup Module - DICOM PS3.3 C.8.8.12

The RT Patient Setup Module contains information describing the positioning
of the patient with respect to the treatment machine, along with any fixation
devices used. It also describes the shielding devices applied to the patient.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.rt_enums import (
    FixationDeviceType, ShieldingDeviceType, SetupTechnique, SetupDeviceType,
    RespiratoryMotionCompensationTechnique, RespiratorySignalSource
)
from ...enums.series_enums import PatientPosition
from ...validators.modules.rt_patient_setup_validator import RTPatientSetupValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class RTPatientSetupModule(BaseModule):
    """RT Patient Setup Module implementation for DICOM PS3.3 C.8.8.12.

    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset for cleaner separation of concerns.
    Contains information describing patient positioning and fixation devices.

    Usage:
        # Create with required elements
        patient_setup = RTPatientSetupModule.from_required_elements(
            patient_setup_sequence=[
                RTPatientSetupModule.create_patient_setup_item(
                    patient_setup_number=1,
                    patient_position=PatientPosition.HFS
                )
            ]
        )

        # Add optional elements using builder methods
        patient_setup.with_optional_elements()

        # Generate dataset for IOD integration
        dataset = patient_setup.to_dataset()

        # Validate
        result = patient_setup.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_setup_sequence: list[Dataset]
    ) -> 'RTPatientSetupModule':
        """Create RTPatientSetupModule from all required (Type 1) data elements.

        Args:
            patient_setup_sequence (list[Dataset]): Sequence of patient setup data (300A,0180) Type 1
                One or more Items shall be included in this Sequence.

        Returns:
            RTPatientSetupModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.PatientSetupSequence = patient_setup_sequence
        return instance
    
    def with_optional_elements(
        self
    ) -> 'RTPatientSetupModule':
        """Add optional (Type 3) elements.
        
        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the Patient Setup Sequence.
        
        Returns:
            RTPatientSetupModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_patient_setup_item(
        patient_setup_number: int,
        patient_position: str | PatientPosition | None = None,
        patient_additional_position: str | None = None,
        patient_setup_label: str | None = None,
        patient_treatment_preparation_sequence: list[Dataset] | None = None,
        referenced_setup_image_sequence: list[Dataset] | None = None,
        fixation_device_sequence: list[Dataset] | None = None,
        shielding_device_sequence: list[Dataset] | None = None,
        setup_technique: str | SetupTechnique | None = None,
        setup_technique_description: str | None = None,
        setup_device_sequence: list[Dataset] | None = None,
        table_top_vertical_setup_displacement: float | None = None,
        table_top_longitudinal_setup_displacement: float | None = None,
        table_top_lateral_setup_displacement: float | None = None,
        motion_synchronization_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create patient setup sequence item.
        
        Args:
            patient_setup_number (int): Identification number of the Patient Setup (300A,0182) Type 1
            patient_position (str | PatientPosition | None): Patient position descriptor (0018,5100) Type 1C
            patient_additional_position (str | None): Additional description of patient position (300A,0184) Type 1C
            patient_setup_label (str | None): User-defined label for Patient Setup (300A,0183) Type 3
            patient_treatment_preparation_sequence (list[Dataset] | None): Treatment preparation procedures (300A,079F) Type 3
            referenced_setup_image_sequence (list[Dataset] | None): Setup verification images (300A,0401) Type 3
            fixation_device_sequence (list[Dataset] | None): Fixation devices used (300A,0190) Type 3
            shielding_device_sequence (list[Dataset] | None): Shielding devices used (300A,01A0) Type 3
            setup_technique (str | SetupTechnique | None): Setup technique used (300A,01B0) Type 3
            setup_technique_description (str | None): Description of setup technique (300A,01B2) Type 3
            setup_device_sequence (list[Dataset] | None): Devices used for patient alignment (300A,01B4) Type 3
            table_top_vertical_setup_displacement (float | None): Vertical displacement (300A,01D2) Type 3
            table_top_longitudinal_setup_displacement (float | None): Longitudinal displacement (300A,01D4) Type 3
            table_top_lateral_setup_displacement (float | None): Lateral displacement (300A,01D6) Type 3
            motion_synchronization_sequence (list[Dataset] | None): Motion synchronization (300A,0410) Type 3
            
        Returns:
            Dataset: Patient setup sequence item
        """
        # Validate conditional requirements
        if patient_position is None and patient_additional_position is None:
            raise ValueError("Either Patient Position or Patient Additional Position must be present")
        
        item = Dataset()
        item.PatientSetupNumber = patient_setup_number
        
        # Add conditional elements
        if patient_position is not None:
            item.PatientPosition = format_enum_value(patient_position)
        if patient_additional_position is not None:
            item.PatientAdditionalPosition = patient_additional_position

        # Add optional elements if provided
        if patient_setup_label is not None:
            item.PatientSetupLabel = patient_setup_label
        if patient_treatment_preparation_sequence is not None:
            item.PatientTreatmentPreparationSequence = patient_treatment_preparation_sequence
        if referenced_setup_image_sequence is not None:
            item.ReferencedSetupImageSequence = referenced_setup_image_sequence
        if fixation_device_sequence is not None:
            item.FixationDeviceSequence = fixation_device_sequence
        if shielding_device_sequence is not None:
            item.ShieldingDeviceSequence = shielding_device_sequence
        if setup_technique is not None:
            item.SetupTechnique = format_enum_value(setup_technique)
        if setup_technique_description is not None:
            item.SetupTechniqueDescription = setup_technique_description
        if setup_device_sequence is not None:
            item.SetupDeviceSequence = setup_device_sequence
        if table_top_vertical_setup_displacement is not None:
            item.TableTopVerticalSetupDisplacement = table_top_vertical_setup_displacement
        if table_top_longitudinal_setup_displacement is not None:
            item.TableTopLongitudinalSetupDisplacement = table_top_longitudinal_setup_displacement
        if table_top_lateral_setup_displacement is not None:
            item.TableTopLateralSetupDisplacement = table_top_lateral_setup_displacement
        if motion_synchronization_sequence is not None:
            item.MotionSynchronizationSequence = motion_synchronization_sequence
        
        return item
    
    @staticmethod
    def create_fixation_device_item(
        fixation_device_type: str | FixationDeviceType,
        fixation_device_label: str = "",
        fixation_device_description: str | None = None,
        fixation_device_position: str | None = None,
        fixation_device_pitch_angle: float | None = None,
        fixation_device_roll_angle: float | None = None,
        accessory_code: str | None = None
    ) -> Dataset:
        """Create fixation device sequence item.
        
        Args:
            fixation_device_type (str | FixationDeviceType): Type of fixation device (300A,0192) Type 1
            fixation_device_label (str): User-defined label for fixation device (300A,0194) Type 2
            fixation_device_description (str | None): Description of fixation device (300A,0196) Type 3
            fixation_device_position (str | None): Position/Notch number (300A,0198) Type 3
            fixation_device_pitch_angle (float | None): Pitch angle in degrees (300A,0199) Type 3
            fixation_device_roll_angle (float | None): Roll angle in degrees (300A,019A) Type 3
            accessory_code (str | None): Bar-code identifier (300A,00F9) Type 3
            
        Returns:
            Dataset: Fixation device sequence item
        """
        item = Dataset()
        item.FixationDeviceType = format_enum_value(fixation_device_type)
        item.FixationDeviceLabel = fixation_device_label
        
        # Add optional elements if provided
        if fixation_device_description is not None:
            item.FixationDeviceDescription = fixation_device_description
        if fixation_device_position is not None:
            item.FixationDevicePosition = fixation_device_position
        if fixation_device_pitch_angle is not None:
            item.FixationDevicePitchAngle = fixation_device_pitch_angle
        if fixation_device_roll_angle is not None:
            item.FixationDeviceRollAngle = fixation_device_roll_angle
        if accessory_code is not None:
            item.AccessoryCode = accessory_code
        
        return item
    
    @staticmethod
    def create_shielding_device_item(
        shielding_device_type: str | ShieldingDeviceType,
        shielding_device_label: str = "",
        shielding_device_description: str | None = None,
        shielding_device_position: str | None = None,
        accessory_code: str | None = None
    ) -> Dataset:
        """Create shielding device sequence item.
        
        Args:
            shielding_device_type (str | ShieldingDeviceType): Type of shielding device (300A,01A2) Type 1
            shielding_device_label (str): User-defined label for shielding device (300A,01A4) Type 2
            shielding_device_description (str | None): Description of shielding device (300A,01A6) Type 3
            shielding_device_position (str | None): Position/Notch number (300A,01A8) Type 3
            accessory_code (str | None): Bar-code identifier (300A,00F9) Type 3
            
        Returns:
            Dataset: Shielding device sequence item
        """
        item = Dataset()
        item.ShieldingDeviceType = format_enum_value(shielding_device_type)
        item.ShieldingDeviceLabel = shielding_device_label
        
        # Add optional elements if provided
        if shielding_device_description is not None:
            item.ShieldingDeviceDescription = shielding_device_description
        if shielding_device_position is not None:
            item.ShieldingDevicePosition = shielding_device_position
        if accessory_code is not None:
            item.AccessoryCode = accessory_code
        
        return item

    @staticmethod
    def create_setup_device_item(
        setup_device_type: str | SetupDeviceType,
        setup_device_label: str = "",
        setup_device_parameter: float = 0.0,
        setup_device_description: str | None = None,
        setup_reference_description: str | None = None,
        accessory_code: str | None = None
    ) -> Dataset:
        """Create setup device sequence item.

        Args:
            setup_device_type (str | SetupDeviceType): Type of setup device (300A,01B6) Type 1
            setup_device_label (str): User-defined label for setup device (300A,01B8) Type 2
            setup_device_parameter (float): Setup parameter for device (300A,01BC) Type 2
            setup_device_description (str | None): Description of setup device (300A,01BA) Type 3
            setup_reference_description (str | None): Description of setup reference (300A,01D0) Type 3
            accessory_code (str | None): Bar-code identifier (300A,00F9) Type 3

        Returns:
            Dataset: Setup device sequence item
        """
        item = Dataset()
        item.SetupDeviceType = format_enum_value(setup_device_type)
        item.SetupDeviceLabel = setup_device_label
        item.SetupDeviceParameter = setup_device_parameter

        # Add optional elements if provided
        if setup_device_description is not None:
            item.SetupDeviceDescription = setup_device_description
        if setup_reference_description is not None:
            item.SetupReferenceDescription = setup_reference_description
        if accessory_code is not None:
            item.AccessoryCode = accessory_code

        return item

    @staticmethod
    def create_motion_synchronization_item(
        respiratory_motion_compensation_technique: str | RespiratoryMotionCompensationTechnique,
        respiratory_signal_source: str | RespiratorySignalSource,
        respiratory_motion_compensation_technique_description: str | None = None,
        respiratory_signal_source_id: str | None = None
    ) -> Dataset:
        """Create motion synchronization sequence item.

        Args:
            respiratory_motion_compensation_technique (str | RespiratoryMotionCompensationTechnique): Motion compensation technique (0018,9170) Type 1
            respiratory_signal_source (str | RespiratorySignalSource): Signal source for respiratory motion (0018,9171) Type 1
            respiratory_motion_compensation_technique_description (str | None): Description of technique (0018,9185) Type 3
            respiratory_signal_source_id (str | None): Device providing signal (0018,9186) Type 3

        Returns:
            Dataset: Motion synchronization sequence item
        """
        item = Dataset()
        item.RespiratoryMotionCompensationTechnique = format_enum_value(respiratory_motion_compensation_technique)
        item.RespiratorySignalSource = format_enum_value(respiratory_signal_source)

        # Add optional elements if provided
        if respiratory_motion_compensation_technique_description is not None:
            item.RespiratoryMotionCompensationTechniqueDescription = respiratory_motion_compensation_technique_description
        if respiratory_signal_source_id is not None:
            item.RespiratorySignalSourceID = respiratory_signal_source_id

        return item

    @staticmethod
    def create_referenced_setup_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        setup_image_comment: str | None = None
    ) -> Dataset:
        """Create referenced setup image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) Type 1
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) Type 1
            setup_image_comment (str | None): Comment on the setup image (300A,0402) Type 3

        Returns:
            Dataset: Referenced setup image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid

        if setup_image_comment is not None:
            item.SetupImageComment = setup_image_comment

        return item

    @staticmethod
    def create_patient_treatment_preparation_item(
        patient_treatment_preparation_procedure_code_sequence: list[Dataset],
        patient_treatment_preparation_method_code_sequence: list[Dataset] | None = None,
        patient_treatment_preparation_device_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create patient treatment preparation sequence item.

        Args:
            patient_treatment_preparation_procedure_code_sequence (list[Dataset]): Procedure codes (0018,002A) Type 1
            patient_treatment_preparation_method_code_sequence (list[Dataset] | None): Method codes (0018,002B) Type 3
            patient_treatment_preparation_device_sequence (list[Dataset] | None): Device sequence (0018,002C) Type 3

        Returns:
            Dataset: Patient treatment preparation sequence item
        """
        item = Dataset()
        item.PatientTreatmentPreparationProcedureCodeSequence = patient_treatment_preparation_procedure_code_sequence

        if patient_treatment_preparation_method_code_sequence is not None:
            item.PatientTreatmentPreparationMethodCodeSequence = patient_treatment_preparation_method_code_sequence
        if patient_treatment_preparation_device_sequence is not None:
            item.PatientTreatmentPreparationDeviceSequence = patient_treatment_preparation_device_sequence

        return item

    @staticmethod
    def create_code_sequence_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        context_identifier: str | None = None,
        context_uid: str | None = None,
        mapping_resource: str | None = None,
        context_group_version: str | None = None,
        context_group_extension_flag: str | None = None,
        context_group_local_version: str | None = None,
        context_group_extension_creator_uid: str | None = None
    ) -> Dataset:
        """Create code sequence item for various code sequences.

        Args:
            code_value (str): Code value (0008,0100) Type 1
            coding_scheme_designator (str): Coding scheme designator (0008,0102) Type 1
            code_meaning (str): Code meaning (0008,0104) Type 1
            coding_scheme_version (str | None): Coding scheme version (0008,0103) Type 1C
            context_identifier (str | None): Context identifier (0008,010F) Type 3
            context_uid (str | None): Context UID (0008,010B) Type 3
            mapping_resource (str | None): Mapping resource (0008,0105) Type 1C
            context_group_version (str | None): Context group version (0008,0106) Type 1C
            context_group_extension_flag (str | None): Extension flag (0008,010B) Type 3
            context_group_local_version (str | None): Local version (0008,0107) Type 1C
            context_group_extension_creator_uid (str | None): Extension creator UID (0008,010D) Type 1C

        Returns:
            Dataset: Code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        # Add conditional and optional elements
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if context_identifier is not None:
            item.ContextIdentifier = context_identifier
        if context_uid is not None:
            item.ContextUID = context_uid
        if mapping_resource is not None:
            item.MappingResource = mapping_resource
        if context_group_version is not None:
            item.ContextGroupVersion = context_group_version
        if context_group_extension_flag is not None:
            item.ContextGroupExtensionFlag = context_group_extension_flag
        if context_group_local_version is not None:
            item.ContextGroupLocalVersion = context_group_local_version
        if context_group_extension_creator_uid is not None:
            item.ContextGroupExtensionCreatorUID = context_group_extension_creator_uid

        return item

    @property
    def has_patient_setups(self) -> bool:
        """Check if patient setup data is present.

        Returns:
            bool: True if Patient Setup Sequence is present
        """
        return hasattr(self._dataset, 'PatientSetupSequence')

    @property
    def patient_setup_count(self) -> int:
        """Get the number of patient setups in this module.

        Returns:
            int: Number of patient setups in Patient Setup Sequence
        """
        setup_sequence = getattr(self._dataset, 'PatientSetupSequence', [])
        return len(setup_sequence)

    def get_patient_setup_numbers(self) -> list[int]:
        """Get list of patient setup numbers present in this module.

        Returns:
            list[int]: List of patient setup numbers
        """
        setup_sequence = getattr(self._dataset, 'PatientSetupSequence', [])
        setup_numbers = []
        for setup_item in setup_sequence:
            setup_number = setup_item.get('PatientSetupNumber')
            if setup_number is not None:
                setup_numbers.append(setup_number)
        return setup_numbers

    def get_patient_setup_by_number(self, setup_number: int) -> Dataset | None:
        """Get patient setup by its number.

        Args:
            setup_number (int): Patient setup number to find

        Returns:
            Dataset | None: Patient setup item or None if not found
        """
        setup_sequence = getattr(self._dataset, 'PatientSetupSequence', [])
        for setup_item in setup_sequence:
            if setup_item.get('PatientSetupNumber') == setup_number:
                return setup_item
        return None

    def has_fixation_devices(self) -> bool:
        """Check if any patient setup has fixation devices.

        Returns:
            bool: True if any fixation devices are present
        """
        setup_sequence = getattr(self._dataset, 'PatientSetupSequence', [])
        for setup_item in setup_sequence:
            if setup_item.get('FixationDeviceSequence'):
                return True
        return False

    def has_motion_synchronization(self) -> bool:
        """Check if any patient setup has motion synchronization.

        Returns:
            bool: True if any motion synchronization is present
        """
        setup_sequence = getattr(self._dataset, 'PatientSetupSequence', [])
        for setup_item in setup_sequence:
            if setup_item.get('MotionSynchronizationSequence'):
                return True
        return False

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Patient Setup Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTPatientSetupValidator.validate(self._dataset, config)
